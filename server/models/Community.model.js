import mongoose from 'mongoose';

const CommunitySchema = new mongoose.Schema(
  {
    name: { type: String, required: true, unique: true },
    description: { type: String },
    iconUrl: { type: String },
    ownerId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    members: [{ type: Schema.Types.ObjectId, ref: 'User' }],
    moderators: [{ type: Schema.Types.ObjectId, ref: 'User' }],
    tags: [{ type: String }],
    isPrivate: { type: Boolean, default: false },
  },
  { timestamps: true },
);

export default mongoose.model('Community', CommunitySchema);
