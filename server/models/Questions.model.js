import mongoose from 'mongoose';

const { Schema } = mongoose;

const QuestionsSchema = mongoose.Schema({
  questionTitle: { type: String, required: 'Question must have title' },
  questionBody: { type: String, required: 'Question must have Body' },
  questionTags: { type: [String], required: 'Question must have Tags' },
  communityId: { type: Schema.Types.ObjectId, ref: 'Community', default: null },
  userId: { type: String },
  askedOn: { type: Date, default: Date.now },
});

export default mongoose.model('Question', QuestionsSchema);
