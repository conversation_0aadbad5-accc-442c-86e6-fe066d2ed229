import mongoose from 'mongoose';

const { Schema } = mongoose;

const PostCommentSchema = new mongoose.Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  postId: { type: Schema.Types.ObjectId, ref: 'Post', required: true },
  commentBody: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model('PostComment', PostCommentSchema);
