import mongoose from 'mongoose';

const { Schema } = mongoose;

const UserFriendRequestSchema = new mongoose.Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  requestToFriendId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  isRequestAccepted: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model('UserFriendRequest', UserFriendRequestSchema);
