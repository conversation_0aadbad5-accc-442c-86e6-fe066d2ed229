import mongoose from "mongoose";

const QuestionsSchema = mongoose.Schema({
  questionTitle: { type: String, required: "Question must have title" },
  questionBody: { type: String, required: "Question must have Body" },
  questionTags: { type: [String], required: "Question must have Tags" },
  userId: { type: String },
  askedOn: { type: Date, default: Date.now },
});

export default mongoose.model("Question", QuestionsSchema);
