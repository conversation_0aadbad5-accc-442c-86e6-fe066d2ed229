import mongoose from 'mongoose';

const { Schema } = mongoose;

const AnswerSchema = new mongoose.Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  questionId: { type: Schema.Types.ObjectId, ref: 'Question', required: true },
  answerBody: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model('Answer', AnswerSchema);
