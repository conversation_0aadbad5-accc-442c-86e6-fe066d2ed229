import mongoose from 'mongoose';

const { Schema } = mongoose;

const QuestionVoteSchema = new mongoose.Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  questionId: { type: Schema.Types.ObjectId, ref: 'Question', required: true },
  type: { type: String, enum: ['upVote', 'downVote'], required: true },
  createdAt: { type: Date, default: Date.now },
});

export default mongoose.model('QuestionVote', QuestionVoteSchema);
