import mongoose from 'mongoose';

const PostSchema = mongoose.Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  postBody: { type: String },
  postMedia: { type: String }, // Cloudinary URL
  cloudinaryPublicId: { type: String }, // For deletion purposes
  mediaType: { type: String },
  postedOn: { type: Date, default: Date.now },
});

export default mongoose.model('Post', PostSchema);
