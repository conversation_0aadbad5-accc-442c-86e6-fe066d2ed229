{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "nodemon index.js"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "mongoose": "^6.8.3", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemon": "^2.0.20"}}