import mongoose from 'mongoose';
import users from '../models/Auth.model.js';
import { deleteFromCloudinary, extractPublicId } from '../config/cloudinaryConfig.js';

export const getAllUsers = async (req, res) => {
  try {
    const allUsers = await users.find();

    // Map users to include all profile information (now embedded in User model)
    const allUserDetails = allUsers.map((user) => ({
      _id: user._id,
      name: user.name,
      about: user.about || '',
      tags: user.tags || [],
      profilePicture: user.profilePicture || '',
      joinedOn: user.joinedOn,
    }));

    res.status(200).json(allUserDetails);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: error.message });
  }
};

export const getUserById = async (req, res) => {
  const { id: _id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(_id)) {
    return res.status(404).send('User not found...');
  }

  try {
    const user = await users.findById(_id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const userWithProfile = {
      _id: user._id,
      name: user.name,
      about: user.about || '',
      tags: user.tags || [],
      profilePicture: user.profilePicture || '',
      joinedOn: user.joinedOn,
    };

    res.status(200).json(userWithProfile);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ message: error.message });
  }
};

export const updateProfile = async (req, res) => {
  const { id: _id } = req.params;
  const { name, about, tags } = req.body;

  if (!mongoose.Types.ObjectId.isValid(_id)) {
    return res.status(404).send('User not found...');
  }

  try {
    // Update user with all profile information (now embedded in User model)
    const updatedUser = await users.findByIdAndUpdate(_id, { $set: { name, about, tags } }, { new: true });

    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Return the updated user with all profile information
    const userWithProfile = {
      _id: updatedUser._id,
      name: updatedUser.name,
      about: updatedUser.about || '',
      tags: updatedUser.tags || [],
      profilePicture: updatedUser.profilePicture || '',
      joinedOn: updatedUser.joinedOn,
    };

    res.status(200).json(userWithProfile);
  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({ message: error.message });
  }
};

export const uploadProfilePicture = async (req, res) => {
  const { id: _id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(_id)) {
    return res.status(404).send('User not found...');
  }

  if (!req.file) {
    return res.status(400).json({ message: 'No file uploaded' });
  }

  try {
    // Check if user exists and get current profile picture
    const currentUser = await users.findById(_id);
    if (!currentUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Delete old profile picture from Cloudinary if it exists
    if (currentUser.profilePicture) {
      try {
        const oldPublicId = extractPublicId(currentUser.profilePicture);
        if (oldPublicId) {
          await deleteFromCloudinary(oldPublicId, 'image');
        }
      } catch (cloudinaryError) {
        console.error('Error deleting old profile picture from Cloudinary:', cloudinaryError);
        // Continue with upload even if deletion fails
      }
    }

    // req.file.path contains the Cloudinary URL
    const profilePictureUrl = req.file.path;

    // Update user with new profile picture (now embedded in User model)
    const updatedUser = await users.findByIdAndUpdate(_id, { $set: { profilePicture: profilePictureUrl } }, { new: true });

    // Return the updated user with all profile information
    const userWithProfile = {
      _id: updatedUser._id,
      name: updatedUser.name,
      about: updatedUser.about || '',
      tags: updatedUser.tags || [],
      profilePicture: updatedUser.profilePicture || '',
      joinedOn: updatedUser.joinedOn,
    };

    res.status(200).json(userWithProfile);
  } catch (error) {
    console.error('Error uploading profile picture:', error);
    res.status(500).json({ message: error.message });
  }
};
