import mongoose from 'mongoose';
import users from '../models/Auth.model.js';
import Profile from '../models/Profile.model.js';
import { deleteFromCloudinary, extractPublicId } from '../config/cloudinaryConfig.js';

export const getAllUsers = async (req, res) => {
  try {
    const allUsers = await users.find();

    // Get profile information for each user
    const allUserDetails = await Promise.all(
      allUsers.map(async (user) => {
        const userProfile = await Profile.findOne({ userId: user._id });

        return {
          _id: user._id,
          name: user.name,
          about: userProfile?.about || '',
          tags: userProfile?.tags || [],
          profilePicture: userProfile?.profilePicture || '',
          joinedOn: user.joinedOn,
        };
      }),
    );

    res.status(200).json(allUserDetails);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: error.message });
  }
};

export const updateProfile = async (req, res) => {
  const { id: _id } = req.params;
  const { name, about, tags } = req.body;

  if (!mongoose.Types.ObjectId.isValid(_id)) {
    return res.status(404).send('User not found...');
  }

  try {
    // Update user name in User model
    const updatedUser = await users.findByIdAndUpdate(_id, { $set: { name: name } }, { new: true });

    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update or create profile information
    const updatedProfile = await Profile.findOneAndUpdate({ userId: _id }, { $set: { about: about, tags: tags } }, { new: true, upsert: true });

    // Return combined user and profile information
    const userWithProfile = {
      ...updatedUser.toObject(),
      about: updatedProfile.about,
      tags: updatedProfile.tags,
      profilePicture: updatedProfile.profilePicture,
    };

    res.status(200).json(userWithProfile);
  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({ message: error.message });
  }
};

export const uploadProfilePicture = async (req, res) => {
  const { id: _id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(_id)) {
    return res.status(404).send('User not found...');
  }

  if (!req.file) {
    return res.status(400).json({ message: 'No file uploaded' });
  }

  try {
    // Check if user exists
    const currentUser = await users.findById(_id);
    if (!currentUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get current profile to check for existing profile picture
    const currentProfile = await Profile.findOne({ userId: _id });

    // Delete old profile picture from Cloudinary if it exists
    if (currentProfile?.profilePicture) {
      try {
        const oldPublicId = extractPublicId(currentProfile.profilePicture);
        if (oldPublicId) {
          await deleteFromCloudinary(oldPublicId, 'image');
        }
      } catch (cloudinaryError) {
        console.error('Error deleting old profile picture from Cloudinary:', cloudinaryError);
        // Continue with upload even if deletion fails
      }
    }

    // req.file.path contains the Cloudinary URL
    const profilePictureUrl = req.file.path;

    // Update or create profile with new profile picture
    const updatedProfile = await Profile.findOneAndUpdate({ userId: _id }, { $set: { profilePicture: profilePictureUrl } }, { new: true, upsert: true });

    // Return combined user and profile information
    const userWithProfile = {
      ...currentUser.toObject(),
      about: updatedProfile.about,
      tags: updatedProfile.tags,
      profilePicture: updatedProfile.profilePicture,
    };

    res.status(200).json(userWithProfile);
  } catch (error) {
    console.error('Error uploading profile picture:', error);
    res.status(500).json({ message: error.message });
  }
};
