import Jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import User from '../models/Auth.model.js';

export const signup = async (req, res) => {
  const { name, email, password } = req.body;

  try {
    const existingUser = await User.findOne({ email });

    if (existingUser) {
      return res.status(409).json({ message: 'User already exists' });
    }

    const hashedPassword = await bcrypt.hash(password, 12);
    const newUser = await User.create({
      name,
      email,
      password: hashedPassword,
      about: '',
      tags: [],
      profilePicture: '',
    });

    const token = Jwt.sign({ email: newUser.email, id: newUser._id }, process.env.JWT_SECRET, { expiresIn: '1h' });

    // Return user with all profile information
    const userWithProfile = {
      _id: newUser._id,
      name: newUser.name,
      email: newUser.email,
      about: newUser.about || '',
      tags: newUser.tags || [],
      profilePicture: newUser.profilePicture || '',
      joinedOn: newUser.joinedOn,
    };

    res.status(200).json({ result: userWithProfile, token });
  } catch (error) {
    console.error('Signup error:', error);
    res.status(500).json({ message: 'Something went wrong...' });
  }
};
export const login = async (req, res) => {
  const { email, password } = req.body;

  try {
    const existingUser = await User.findOne({ email });
    if (!existingUser) {
      return res.status(404).json({ message: "User doesn't exist" });
    }

    const isPasswordCorrect = await bcrypt.compare(password, existingUser.password);

    if (!isPasswordCorrect) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    const token = Jwt.sign({ email: existingUser.email, id: existingUser._id }, process.env.JWT_SECRET, { expiresIn: '1h' });

    // Return user with embedded profile information
    const userWithProfile = {
      _id: existingUser._id,
      name: existingUser.name,
      email: existingUser.email,
      about: existingUser.about || '',
      tags: existingUser.tags || [],
      profilePicture: existingUser.profilePicture || '',
      joinedOn: existingUser.joinedOn,
    };

    res.status(200).json({ result: userWithProfile, token });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: error.message });
  }
};
