import Jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import User from '../models/Auth.model.js';

export const signup = async (req, res) => {
  const { name, email, password } = req.body;

  try {
    const existingUser = await User.findOne({ email });

    if (existingUser) {
      return res.status(409).json({ message: 'User already exists' });
    }

    const hashedPassword = await bcrypt.hash(password, 12);
    const newUser = await User.create({ name, email, password: hashedPassword, about: '', tags: [], profilePicture: '' });

    const token = Jwt.sign({ email: newUser.email, id: newUser._id }, process.env.JWT_SECRET, { expiresIn: '1h' });

    res.status(200).json({ result: newUser, token });
  } catch (error) {
    console.error('Signup error:', error);
    res.status(500).json({ message: 'Something went wrong...' });
  }
};
export const login = async (req, res) => {
  const { email, password } = req.body;

  try {
    const existingUser = await User.findOne({ email });
    if (!existingUser) {
      return res.status(404).json({ message: "User doesn't exist" });
    }

    const isPasswordCorrect = await bcrypt.compare(password, existingUser.password);

    if (!isPasswordCorrect) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Get user profile information
    const userProfile = await Profile.findOne({ userId: existingUser._id });

    const token = Jwt.sign({ email: existingUser.email, id: existingUser._id }, process.env.JWT_SECRET, { expiresIn: '1h' });

    // Return user with profile information
    const userWithProfile = {
      ...existingUser.toObject(),
      about: userProfile?.about || '',
      tags: userProfile?.tags || [],
      profilePicture: userProfile?.profilePicture || '',
    };

    res.status(200).json({ result: userWithProfile, token });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: error.message });
  }
};
