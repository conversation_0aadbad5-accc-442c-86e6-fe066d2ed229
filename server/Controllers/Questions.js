import Questions from '../models/Questions.model.js';
import mongoose from 'mongoose';
import User from '../models/Auth.model.js';
import Answer from '../models/Answer.model.js';
import QuestionVote from '../models/QuestionVote.model.js';

export const AskQuestion = async (req, res) => {
  const postQuestionData = req.body;
  const postQuestion = new Questions(postQuestionData);

  try {
    await postQuestion.save();
    res.status(200).json('Posted a question succesfully');
  } catch (error) {
    console.log(error);
    res.status(409).json("coudn'nt post a new question");
  }
};

export const getAllQuestions = async (req, res) => {
  try {
    const questionList = await Questions.find().sort({ askedOn: -1 });

    // Add profile pictures to questions and get answers from separate collection
    const questionsWithDetails = await Promise.all(
      questionList.map(async (question) => {
        // Get profile picture for question author
        const questionAuthor = await User.findById(question.userId);

        // Get answers for this question from separate Answer collection
        const answers = await Answer.find({ questionId: question._id }).populate('userId', 'name').sort({ createdAt: -1 });

        // Get vote counts for this question
        const upVotes = await QuestionVote.countDocuments({
          questionId: question._id,
          type: 'upVote',
        });
        const downVotes = await QuestionVote.countDocuments({
          questionId: question._id,
          type: 'downVote',
        });

        return {
          ...question.toObject(),
          profilePicture: questionAuthor?.profilePicture || null,
          userPosted: questionAuthor?.name || null,
          answer: answers,
          noOfAnswers: answers.length,
          upVote: Array(upVotes).fill(''), // For compatibility with frontend
          downVote: Array(downVotes).fill(''), // For compatibility with frontend
          upVoteCount: upVotes,
          downVoteCount: downVotes,
        };
      }),
    );

    res.status(200).json(questionsWithDetails);
  } catch (error) {
    console.error('Error fetching questions:', error);
    res.status(404).json({ message: error.message });
  }
};

export const getQuestionById = async (req, res) => {
  const { id: _id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(_id)) {
    return res.status(404).send('Question unavailable...');
  }

  try {
    const question = await Questions.findById(_id);
    if (!question) {
      return res.status(404).json({ message: 'Question not found' });
    }

    // Get profile picture for question author
    const questionAuthor = await User.findById(question.userId);

    // Get answers for this question from separate Answer collection
    const answers = await Answer.find({ questionId: _id }).populate('userId', 'name').sort({ createdAt: -1 });

    const mappedAnswers = answers.map((answer) => ({
      ...answer.toObject(),
      userId: answer.userId._id,
      userAnswered: answer.userId.name,
      profilePicture: answer.userId.profilePicture || null,
    }));

    // Get vote counts for this question
    const upVotes = await QuestionVote.countDocuments({
      questionId: _id,
      type: 'upVote',
    });

    const downVotes = await QuestionVote.countDocuments({
      questionId: _id,
      type: 'downVote',
    });
    console.log({ questionAuthor });

    res.status(200).json({
      ...question.toObject(),
      profilePicture: questionAuthor?.profilePicture || null,
      userPosted: questionAuthor?.name || null,
      answer: mappedAnswers,
      noOfAnswers: mappedAnswers.length,
      upVote: Array(upVotes).fill(''), // For compatibility with frontend
      downVote: Array(downVotes).fill(''), // For compatibility with frontend
      upVoteCount: upVotes,
      downVoteCount: downVotes,
    });
  } catch (error) {
    console.error('Error fetching question:', error);
    res.status(404).json({ message: error.message });
  }
};

export const deleteQuestion = async (req, res) => {
  const { id: _id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(_id)) {
    return res.status(404).send('Question unavailable...');
  }

  try {
    // Delete the question
    const deletedQuestion = await Questions.findByIdAndRemove(_id);

    if (!deletedQuestion) {
      return res.status(404).json({ message: 'Question not found' });
    }

    // Delete all associated answers
    await Answer.deleteMany({ questionId: _id });

    res.status(200).json({ message: 'Question and associated answers deleted successfully' });
  } catch (error) {
    console.error('Error deleting question:', error);
    res.status(500).json({ message: error.message });
  }
};

export const voteQuestion = async (req, res) => {
  const { id: _id } = req.params;
  const { value, userId } = req.body;

  if (!mongoose.Types.ObjectId.isValid(_id)) {
    return res.status(404).send('Question unavailable...');
  }

  if (!mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(404).send('Invalid user ID...');
  }

  try {
    // Check if question exists
    const question = await Questions.findById(_id);
    if (!question) {
      return res.status(404).json({ message: 'Question not found' });
    }

    // Check if user has already voted
    const existingVote = await QuestionVote.findOne({
      userId,
      questionId: _id,
    });

    if (existingVote) {
      if (existingVote.type === value) {
        // User is removing their vote
        await QuestionVote.findByIdAndDelete(existingVote._id);
        res.status(200).json({ message: 'Vote removed successfully' });
      } else {
        // User is changing their vote
        existingVote.type = value;
        await existingVote.save();
        res.status(200).json({ message: 'Vote updated successfully' });
      }
    } else {
      // User is voting for the first time
      const newVote = new QuestionVote({
        userId,
        questionId: _id,
        type: value,
      });
      await newVote.save();
      res.status(200).json({ message: 'Vote added successfully' });
    }
  } catch (error) {
    console.error('Error voting on question:', error);
    res.status(500).json({ message: error.message });
  }
};
