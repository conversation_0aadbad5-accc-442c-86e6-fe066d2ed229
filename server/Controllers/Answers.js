import mongoose from 'mongoose';
import Questions from '../models/Questions.js';
import Answer from '../models/Answer.model.js';

export const postAnswer = async (req, res) => {
  const { id: _id } = req.params;
  const { answerBody, userId } = req.body;

  if (!mongoose.Types.ObjectId.isValid(_id)) {
    return res.status(404).send('Question unavailable...');
  }

  if (!mongoose.Types.ObjectId.isValid(userId)) {
    return res.status(404).send('Invalid user ID...');
  }

  try {
    // Check if question exists
    const question = await Questions.findById(_id);
    if (!question) {
      return res.status(404).send('Question not found...');
    }

    // Create new answer
    const newAnswer = new Answer({
      userId,
      questionId: _id,
      answerBody,
    });

    const savedAnswer = await newAnswer.save();

    // Populate the user information for the response
    const populatedAnswer = await Answer.findById(savedAnswer._id).populate('userId', 'name');

    res.status(200).json(populatedAnswer);
  } catch (error) {
    console.error('Error posting answer:', error);
    res.status(400).json({ message: error.message });
  }
};

export const getAnswersForQuestion = async (req, res) => {
  const { id: questionId } = req.params;

  if (!mongoose.Types.ObjectId.isValid(questionId)) {
    return res.status(404).send('Question unavailable...');
  }

  try {
    const answers = await Answer.find({ questionId }).populate('userId', 'name').sort({ createdAt: -1 });

    res.status(200).json(answers);
  } catch (error) {
    console.error('Error fetching answers:', error);
    res.status(500).json({ message: error.message });
  }
};

export const deleteAnswer = async (req, res) => {
  const { id: answerId } = req.params;

  if (!mongoose.Types.ObjectId.isValid(answerId)) {
    return res.status(404).send('Answer unavailable...');
  }

  try {
    const deletedAnswer = await Answer.findByIdAndDelete(answerId);

    if (!deletedAnswer) {
      return res.status(404).json({
        message: 'Answer not found',
      });
    }

    res.status(200).json({
      message: 'Answer deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting answer:', error);
    res.status(500).json({ message: error.message });
  }
};
