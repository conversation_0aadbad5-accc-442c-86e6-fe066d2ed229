{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@tanstack/react-query": "^5.59.0", "@tanstack/react-query-devtools": "^5.59.0", "antd": "^5.26.0", "axios": "^1.7.7", "copy-to-clipboard": "^3.3.3", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.28.0", "web-vitals": "^4.2.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "@vitejs/plugin-react": "^4.3.4", "vite": "^6.0.3", "vitest": "^3.1.4"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "build:netlify": "vite build && cp public/_redirects build/", "preview": "vite preview", "test": "vitest"}}