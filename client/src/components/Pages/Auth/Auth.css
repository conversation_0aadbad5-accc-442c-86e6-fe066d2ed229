.auth-section
{
 min-height: 100vh;
 margin: 20px auto;
 background-color: #f1f2f3;
 display: flex;
 justify-content: center;
 align-items: center;   
}
.auth-container-1
{
  padding: 20px; 
  margin-right: 30px; 
}
.login-logo
{
padding: 20px 30px;    
}
.auth-container-2
{
 min-height: 20%;
 display: flex;
 flex-direction: column;
 justify-content: center;
 align-items: center;   
}
.auth-container-2 form
{
 width: 100%;
 padding: 20px;
 background-color: white;
 border-radius: 10px;
 display: flex;
 justify-content: space-evenly;
 flex-direction: column;
 box-shadow: 0px 10px 25px rgb(0 0 0 / 5%),
 0px 20px 48px rgb(0 0 0 / 5%),
 0px 1px 4px rgb(0 0 0 / 10%);  
}
.auth-container-2 form label input
{
padding: 10px;
width: calc(100% - 20px);
border: 1px solid #0000003e;
font-size: 13px;
}
.auth-container-2 form label:nth-child(1) h4,
.auth-container-2 form label:nth-child(2) h4,
.auth-container-2 form label:nth-child(3) h4
{
    margin: 10px 0px 5px 0px;
}
.auth-container-2 form label:nth-child(4)
{
display: flex;
}
.auth-container-2 form label:nth-child(4) input
{
 height: fit-content;
 width: 15%;
 margin: 13px 0px;    
}
.auth-btn
{
 margin-top: 10px;
 padding: 10px 5px;
 background-color: #009dff;
 border: 1px solid #009dff;
 color: white;
 border-radius: 5px;
 cursor: pointer;
 transition: .2s;
 font-size: 13px;
 font-weight: 500;   
}
.auth-btn:hover
{
  background-color: #018ce3;  
}
.handle-switch-btn
{
   background-color: transparent;
   color: #009dff;
   border: none;
   font-size: 13px;
   cursor: pointer; 
}

@media screen and (max-width: 820px) {
  .auth-container-1 {
    display: none;
  }
}