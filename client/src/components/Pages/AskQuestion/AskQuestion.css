.ask-question-box {
  min-height: 100vh;
  background-color: #f1f2f3;
  padding: 0px 30px 30px 30px;
}
.ask-ques-container {
  margin-top: 20px;
  width: 100%;
  padding: 10px 0 10px 0;
}
.ask-ques-container h1 {
  padding: 32px 0px 10px 0px;
  margin-bottom: 0;
}
.ask-ques-container form .ask-form-container {
  padding: 20px;
  background-color: white;
  border-radius: 3px;
  box-shadow: 0px 10px 25px rgb(0 0 0 / 5%), 0px 20px 48px rgb(0 0 0 / 5%),
    0px 1px 4px rgb(0 0 0 / 10%);
}
.ask-form-container label h4 {
  margin-bottom: 0%;
}
.ask-form-container label p {
  margin: 0%;
  font-size: 13px;
  padding: 3px 0px;
}
.ask-form-container label input,
.ask-form-container label .textarea {
  padding: 10px;
  resize: vertical;
  border: 1px solid #0000003e;
  width: calc(100% - 20px);
  font-family: "Roboto", sans-serif;
}
.review-btn {
  margin-top: 20px;
  background-color: #009dff;
  padding: 10px;
  color: white;
  border-radius: 3px;
  transition: 0.3s;
  cursor: pointer;
  border: 1px solid #009dff;
}
.review-btn:hover {
  background-color: #0086d8;
}
