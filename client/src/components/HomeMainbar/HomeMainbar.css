.main-bar {
  width: calc(100% - 300px);
  float: left;
  margin: 40px 0px;
}
.mainbar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.mainbar-header h1 {
  font-weight: 400;
}
.mainbar-header .ask-question {
  text-decoration: none;
  background-color: #009dff;
  margin-top: 5px;
  padding: 10px 15px;
  border-radius: 4px;
  color: white;
  transition: 0.3s;
  border: none;
}
.mainbar-header .ask-question:hover {
  background-color: #0086d8;
}
.display-question-container {
  min-height: 80px;
  width: 100%;
  display: flex;
  align-items: center;
  background-color: #fdf7e2;
  border-bottom: 1px solid #edeff0;
  padding: 10px 0px;
  justify-content: space-between;
  box-shadow: 4px 4px 15px rgba(0, 0, 0, 0.07);
}
.display-question-container .display-vote-ans {
  padding: 20px;
}
.display-question-container .display-vote-ans p {
  margin: 0%;
  text-align: center;
}
.display-question-details {
  flex-grow: 1;
  /* padding: 0px 20px;   */
}
.display-question-details p {
  padding: 4px;
  margin: 0px;
}
.display-tags {
  display: flex;
  flex-wrap: wrap;
}
.question-title-link {
  color: #037ecb;
  text-decoration: none;
  transition: 0.3s;
}
.question-title-link:hover {
  color: #009dff;
}
.display-tags p {
  margin: 2px;
  padding: 4px;
  font-size: 13px;
  color: #39739d;
  background-color: #edeff0;
}
.display-tags-time {
  display: flex;
  padding: 10px;
  justify-content: space-between;
  flex-wrap: wrap;
}

@media screen and (max-width: 1020px) {
  .main-bar {
    width: 100%;
  }
}

@media screen and (max-width: 740px) {
  .display-question-container .display-votes-ans {
    padding: 10px;
  }
}
