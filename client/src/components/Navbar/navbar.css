nav {
  min-height: 50px;
  width: 100%;
  margin: 0% auto;
  border-top: 3px solid #ef8236;
  box-shadow: 0px 1px 5px #00000033;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 11;
  background-color: #f8f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
}
.navbar {
  height: 100%;
  width: 100%;
  max-width: 1440px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar .navbar-1 {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.navbar .navbar-2 {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding-right: 1rem;
}

.nav-logo {
  padding: 5px 25px;
}
.nav-item {
  text-decoration: none;
  font-size: small;
  font-weight: 500;
  color: rgb(69, 69, 69);
  transition: 0.2s;
}
.nav-btn {
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 20px;
}
.nav-btn:hover {
  background-color: rgb(226, 226, 226);
}
.navbar form {
  flex-grow: 1;
  padding: 0px 12px;
  position: relative;
}
.navbar form input {
  min-width: 90%;
  margin: 0;
  padding: 8px 10px 8px 33px;
  font-size: 14px;
  border: 1px solid #0000003e;
  border-radius: 3px;
  outline: none;
}
.navbar form .search-icon {
  position: absolute;
  left: 22px;
  top: 7px;
}
.nav-link {
  padding: 7px 13px;
  border: 1px solid blue;
  border-radius: 3px;
  background-color: #e7f8fe;
  transform: 0.2s;
  cursor: pointer;
}
.nav-link:hover {
  background-color: #d3e4eb;
}

@media screen and (max-width: 500px) {
  .navbar .navbar-1 form {
    display: none;
  }
}

@media screen and (max-width: 768px) {
  .slide-in-icon {
    display: block;
  }
  .nav-btn {
    display: none !important;
  }
}

@media screen and (max-width: 720px) {
  .navbar .navbar-1 .res-nav {
    display: none;
  }
}
