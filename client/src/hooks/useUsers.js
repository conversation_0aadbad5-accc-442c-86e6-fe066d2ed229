import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import * as api from "../API";
import { jwtDecode } from "jwt-decode";

// Fetch all users
export const useUsers = () => {
  return useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const response = await api.fetchAllUsers();
      return response.data;
    },
  });
};

export const useUserById = async () => {
  const token = localStorage.getItem("token");
  const decodedToken = jwtDecode(token);
  const id = decodedToken.id;
  console.log(decodedToken);

  return useQuery({
    queryKey: ["user", id],
    enabled: Boolean(id),
    queryFn: async () => {
      if (token && decodedToken) {
        const user = await api.getUserById(id);

        if (decodedToken.exp * 1000 < new Date().getTime()) {
          handleLogOut();
          return {};
        }

        console.log(user?.data);
        return user?.data;
      } else {
        return {};
      }
    },
  });
};

// Update profile mutation
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, updateData }) => api.updateProfile(id, updateData),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.setQueryData(["currentUser"], response.data);
    },
    onError: (error) => {
      console.log(error);
    },
  });
};

// Upload profile picture mutation
export const useUploadProfilePicture = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, formData }) => api.uploadProfilePicture(id, formData),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.setQueryData(["currentUser"], response.data);
    },
    onError: (error) => {
      console.log(error);
    },
  });
};
